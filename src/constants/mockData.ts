import { Post } from "@/types";

export const MOCK_POSTS_DATA: Record<string, Post[]> = {
  "techcorp": [
    {
      id: "1",
      brandId: "techcorp",
      title: "New Product Launch",
      content: "Excited to announce our latest innovation that will revolutionize how teams collaborate...",
      platform: "LinkedIn",
      timestamp: "2 hours ago",
      engagement: { likes: 124, comments: 18, shares: 32 },
      comments: [
        {
          id: "c1",
          author: "<PERSON>",
          content: "This looks amazing! When will it be available for enterprise customers?",
          timestamp: "1 hour ago",
          aiReply: "Thanks for your interest, <PERSON>! Our enterprise solution will be rolling out in Q2 2024. I'd be happy to connect you with our enterprise team to discuss early access opportunities.",
          sentiment: "positive"
        },
        {
          id: "c2",
          author: "<PERSON>",
          content: "Pricing seems steep for small businesses. Any plans for a starter tier?",
          timestamp: "45 minutes ago",
          aiReply: "Great question, <PERSON>! We understand the importance of making our solution accessible to businesses of all sizes. We're currently working on a starter plan that will be announced soon. Would you like me to add you to our updates list?",
          sentiment: "neutral"
        }
      ]
    },
    {
      id: "2",
      brandId: "techcorp",
      title: "Company Culture Spotlight",
      content: "Behind the scenes at our engineering team's innovation day. Amazing ideas brewing! 🚀",
      platform: "Twitter",
      timestamp: "4 hours ago",
      engagement: { likes: 89, comments: 12, shares: 15 },
      comments: [
        {
          id: "c3",
          author: "Alex Rivera",
          content: "Love seeing companies invest in innovation time! Are you hiring remote engineers?",
          timestamp: "3 hours ago",
          aiReply: "Absolutely! We're always looking for talented engineers. Check out our careers page for current remote opportunities, and feel free to reach out if you have specific questions about our engineering culture.",
          sentiment: "positive"
        }
      ]
    }
  ],
  "innovate-labs": [
    {
      id: "3",
      brandId: "innovate-labs",
      title: "AI Research Breakthrough",
      content: "Our latest research in machine learning has achieved 95% accuracy in natural language processing...",
      platform: "LinkedIn",
      timestamp: "1 hour ago",
      engagement: { likes: 67, comments: 8, shares: 22 },
      comments: [
        {
          id: "c4",
          author: "Dr. Emily Watson",
          content: "Impressive results! Could you share more details about the training methodology?",
          timestamp: "30 minutes ago",
          aiReply: "Thank you for your interest, Dr. Watson! We used a novel transformer architecture with custom attention mechanisms. I'd be happy to discuss the technical details - would you like me to connect you with our research team?",
          sentiment: "positive"
        }
      ]
    }
  ]
};
