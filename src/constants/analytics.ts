import { Target, Zap, MessageSquare, Clock, TrendingUp, BarChart3 } from "lucide-react";
import { AnalyticsMetric } from "@/types";

export const ANALYTICS_METRICS: Omit<AnalyticsMetric, 'icon'>[] = [
  {
    title: "AI Accuracy",
    value: "94.2%",
    change: "+2.1%",
    trend: "up",
    description: "Responses approved without edits"
  },
  {
    title: "Response Time",
    value: "1.3s",
    change: "-0.4s",
    trend: "up",
    description: "Average AI generation time"
  },
  {
    title: "Messages Processed",
    value: "2,847",
    change: "+18.2%",
    trend: "up",
    description: "Total messages this month"
  },
  {
    title: "Time Saved",
    value: "127.3h",
    change: "+22.5h",
    trend: "up",
    description: "Estimated hours saved this month"
  },
  {
    title: "Engagement Rate",
    value: "87.5%",
    change: "+4.3%",
    trend: "up",
    description: "Comments receiving replies"
  },
  {
    title: "Learning Progress",
    value: "8.7/10",
    change: "+0.5",
    trend: "up",
    description: "AI model adaptation score"
  }
];

export const ANALYTICS_ICONS = [
  Target,
  Zap,
  MessageSquare,
  Clock,
  TrendingUp,
  BarChart3
];
