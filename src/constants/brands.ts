import { Brand } from "@/types";

export const BRANDS: Brand[] = [
  { id: "techcorp", name: "TechCorp", pendingReplies: 12, status: "active" },
  { id: "innovate-labs", name: "Innovate Labs", pendingReplies: 8, status: "active" },
  { id: "future-systems", name: "Future Systems", pendingReplies: 15, status: "active" },
  { id: "digital-solutions", name: "Digital Solutions", pendingReplies: 3, status: "paused" },
  { id: "creative-agency", name: "Creative Agency", pendingReplies: 7, status: "active" },
];

export const BRAND_NAMES: Record<string, string> = {
  "techcorp": "TechCorp",
  "innovate-labs": "Innovate Labs", 
  "future-systems": "Future Systems",
  "digital-solutions": "Digital Solutions",
  "creative-agency": "Creative Agency"
};
