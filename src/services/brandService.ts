import { Brand, <PERSON> } from "@/types";
import { BRANDS } from "@/constants/brands";
import { MOCK_POSTS_DATA } from "@/constants/mockData";

export class BrandService {
  static getAllBrands(): Brand[] {
    return BRANDS;
  }

  static getBrandById(brandId: string): Brand | undefined {
    return BRANDS.find(brand => brand.id === brandId);
  }

  static getBrandPosts(brandId: string): Post[] {
    return MOCK_POSTS_DATA[brandId] || [];
  }

  static async fetchBrandPosts(brandId: string): Promise<Post[]> {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(this.getBrandPosts(brandId));
      }, 500);
    });
  }

  static sendReply(postId: string, commentId: string, reply: string): Promise<void> {
    // Simulate API call for sending reply
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log("Reply sent:", { postId, commentId, reply });
        resolve();
      }, 1000);
    });
  }

  static updateReply(postId: string, commentId: string, reply: string): Promise<void> {
    // Simulate API call for updating reply
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log("Reply updated:", { postId, commentId, reply });
        resolve();
      }, 1000);
    });
  }
}
