// Brand related types
export interface Brand {
  id: string;
  name: string;
  pendingReplies: number;
  status: "active" | "paused";
}

// Comment and Post types
export interface Comment {
  id: string;
  author: string;
  content: string;
  timestamp: string;
  aiReply: string;
  sentiment: "positive" | "neutral" | "negative";
}

export interface Post {
  id: string;
  title: string;
  content: string;
  platform: string;
  timestamp: string;
  engagement: {
    likes: number;
    comments: number;
    shares: number;
  };
  comments: Comment[];
  brandId: string;
}

// Analytics types
export interface AnalyticsMetric {
  title: string;
  value: string;
  change: string;
  trend: "up" | "down";
  icon: React.ReactNode;
  description: string;
}

// Component prop types
export interface PostCardProps {
  post: Post;
  onSendReply: (postId: string, commentId: string, reply: string) => void;
  onEditReply: (postId: string, commentId: string, reply: string) => void;
}

export interface AnalyticsCardProps {
  title: string;
  value: string;
  change: string;
  trend: "up" | "down";
  icon: React.ReactNode;
  description: string;
}

// Layout types
export interface LayoutProps {
  children: React.ReactNode;
}
