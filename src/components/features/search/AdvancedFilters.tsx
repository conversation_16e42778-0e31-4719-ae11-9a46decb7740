import { useState } from "react";
import { Filter, Calendar, Hash, User, MessageSquare, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

export interface FilterOptions {
  platform?: string;
  sentiment?: string;
  dateRange?: string;
  engagement?: string;
  author?: string;
  hasReplies?: boolean;
}

export interface AdvancedFiltersProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  onClearFilters: () => void;
}

const PLATFORMS = [
  { value: 'all', label: 'All Platforms' },
  { value: 'twitter', label: 'Twitter' },
  { value: 'linkedin', label: 'LinkedIn' },
  { value: 'facebook', label: 'Facebook' },
  { value: 'instagram', label: 'Instagram' },
];

const SENTIMENTS = [
  { value: 'all', label: 'All Sentiments' },
  { value: 'positive', label: 'Positive' },
  { value: 'neutral', label: 'Neutral' },
  { value: 'negative', label: 'Negative' },
];

const DATE_RANGES = [
  { value: 'all', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' },
];

const ENGAGEMENT_LEVELS = [
  { value: 'all', label: 'All Engagement' },
  { value: 'high', label: 'High (100+)' },
  { value: 'medium', label: 'Medium (10-99)' },
  { value: 'low', label: 'Low (0-9)' },
];

export function AdvancedFilters({
  filters,
  onFiltersChange,
  onClearFilters,
}: AdvancedFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  const updateFilter = (key: keyof FilterOptions, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value === 'all' ? undefined : value,
    });
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => 
      value !== undefined && value !== 'all' && value !== ''
    ).length;
  };

  const getFilterSummary = () => {
    const activeFilters = [];
    
    if (filters.platform && filters.platform !== 'all') {
      activeFilters.push(filters.platform);
    }
    if (filters.sentiment && filters.sentiment !== 'all') {
      activeFilters.push(filters.sentiment);
    }
    if (filters.dateRange && filters.dateRange !== 'all') {
      activeFilters.push(filters.dateRange);
    }
    if (filters.engagement && filters.engagement !== 'all') {
      activeFilters.push(`${filters.engagement} engagement`);
    }
    if (filters.author) {
      activeFilters.push(`by ${filters.author}`);
    }
    if (filters.hasReplies) {
      activeFilters.push('has replies');
    }

    return activeFilters;
  };

  const activeFiltersCount = getActiveFiltersCount();
  const filterSummary = getFilterSummary();

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Filter className="w-4 h-4 mr-2" />
          Filters
          {activeFiltersCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
            >
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-0" align="end">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Advanced Filters</h3>
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="text-xs"
              >
                Clear all
              </Button>
            )}
          </div>
        </div>

        <div className="p-4 space-y-4">
          {/* Platform Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Hash className="w-4 h-4" />
              Platform
            </Label>
            <Select
              value={filters.platform || 'all'}
              onValueChange={(value) => updateFilter('platform', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {PLATFORMS.map((platform) => (
                  <SelectItem key={platform.value} value={platform.value}>
                    {platform.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Sentiment Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Sentiment
            </Label>
            <Select
              value={filters.sentiment || 'all'}
              onValueChange={(value) => updateFilter('sentiment', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {SENTIMENTS.map((sentiment) => (
                  <SelectItem key={sentiment.value} value={sentiment.value}>
                    {sentiment.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date Range Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Date Range
            </Label>
            <Select
              value={filters.dateRange || 'all'}
              onValueChange={(value) => updateFilter('dateRange', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {DATE_RANGES.map((range) => (
                  <SelectItem key={range.value} value={range.value}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Engagement Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Engagement Level</Label>
            <Select
              value={filters.engagement || 'all'}
              onValueChange={(value) => updateFilter('engagement', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {ENGAGEMENT_LEVELS.map((level) => (
                  <SelectItem key={level.value} value={level.value}>
                    {level.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Author Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium flex items-center gap-2">
              <User className="w-4 h-4" />
              Author
            </Label>
            <Input
              placeholder="Filter by author name..."
              value={filters.author || ''}
              onChange={(e) => updateFilter('author', e.target.value)}
            />
          </div>
        </div>

        {/* Active Filters Summary */}
        {filterSummary.length > 0 && (
          <>
            <Separator />
            <div className="p-4">
              <Label className="text-sm font-medium mb-2 block">Active Filters</Label>
              <div className="flex flex-wrap gap-2">
                {filterSummary.map((filter, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {filter}
                  </Badge>
                ))}
              </div>
            </div>
          </>
        )}
      </PopoverContent>
    </Popover>
  );
}
