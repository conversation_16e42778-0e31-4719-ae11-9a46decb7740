import { useState, useEffect, useRef } from "react";
import { Search, X, Clock, TrendingUp } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

export interface SearchSuggestion {
  id: string;
  text: string;
  type: 'recent' | 'trending' | 'suggestion';
  count?: number;
}

export interface SearchWithSuggestionsProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  suggestions?: SearchSuggestion[];
  recentSearches?: string[];
  onSearch?: (query: string) => void;
  className?: string;
}

export function SearchWithSuggestions({
  value,
  onChange,
  placeholder = "Search...",
  suggestions = [],
  recentSearches = [],
  onSearch,
  className,
}: SearchWithSuggestionsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<SearchSuggestion[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!value.trim()) {
      // Show recent searches when no query
      const recentSuggestions: SearchSuggestion[] = recentSearches.map((search, index) => ({
        id: `recent-${index}`,
        text: search,
        type: 'recent',
      }));
      setFilteredSuggestions(recentSuggestions.slice(0, 5));
    } else {
      // Filter suggestions based on query
      const filtered = suggestions.filter(suggestion =>
        suggestion.text.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredSuggestions(filtered.slice(0, 8));
    }
  }, [value, suggestions, recentSearches]);

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    onChange(suggestion.text);
    setIsOpen(false);
    onSearch?.(suggestion.text);
  };

  const handleClear = () => {
    onChange('');
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setIsOpen(false);
      onSearch?.(value);
    } else if (e.key === 'Escape') {
      setIsOpen(false);
    }
  };

  const getSuggestionIcon = (type: SearchSuggestion['type']) => {
    switch (type) {
      case 'recent':
        return <Clock className="w-4 h-4 text-muted-foreground" />;
      case 'trending':
        return <TrendingUp className="w-4 h-4 text-primary" />;
      default:
        return <Search className="w-4 h-4 text-muted-foreground" />;
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div className={cn("relative", className)}>
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            ref={inputRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={() => setIsOpen(true)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="pl-10 pr-10"
          />
          {value && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-0" align="start">
        {filteredSuggestions.length > 0 ? (
          <div className="py-2">
            {!value.trim() && recentSearches.length > 0 && (
              <div className="px-3 py-2 text-xs font-medium text-muted-foreground border-b">
                Recent Searches
              </div>
            )}
            {value.trim() && (
              <div className="px-3 py-2 text-xs font-medium text-muted-foreground border-b">
                Suggestions
              </div>
            )}
            
            {filteredSuggestions.map((suggestion) => (
              <button
                key={suggestion.id}
                onClick={() => handleSuggestionClick(suggestion)}
                className="w-full px-3 py-2 text-left hover:bg-muted transition-colors flex items-center gap-3"
              >
                {getSuggestionIcon(suggestion.type)}
                <span className="flex-1 text-sm">{suggestion.text}</span>
                {suggestion.count && (
                  <Badge variant="secondary" className="text-xs">
                    {suggestion.count}
                  </Badge>
                )}
              </button>
            ))}
          </div>
        ) : value.trim() ? (
          <div className="px-3 py-8 text-center text-sm text-muted-foreground">
            No suggestions found
          </div>
        ) : (
          <div className="px-3 py-8 text-center text-sm text-muted-foreground">
            Start typing to search
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
