import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingButton } from "@/components/ui/loading-button";
import { ProgressIndicator } from "@/components/ui/progress-indicator";
import { Textarea } from "@/components/ui/textarea";
import {
  MessageCircle,
  ChevronDown,
  ChevronUp,
  Send,
  Edit3,
  Brain,
  Heart,
  Share,
  ExternalLink,
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Copy,
  MoreHorizontal
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { PostCardProps } from "@/types";
import { getSentimentColor, getPlatformColor } from "@/utils";
import { useLoading } from "@/hooks/use-loading";
import { useNotifications } from "@/hooks/use-notifications";
import { cn } from "@/lib/utils";

export function PostCard({ post, onSendReply, onEditReply }: PostCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [editingReply, setEditingReply] = useState<string | null>(null);
  const [editContent, setEditContent] = useState<string>("");
  const [generatingReply, setGeneratingReply] = useState<string | null>(null);
  const [replyProgress, setReplyProgress] = useState(0);

  const { executeAsync: executeReplyAction } = useLoading();
  const { showToast } = useNotifications();

  const handleEditReply = (commentId: string, currentReply: string) => {
    setEditingReply(commentId);
    setEditContent(currentReply);
  };

  const handleSaveEdit = async (commentId: string) => {
    await executeReplyAction(
      async () => {
        await onEditReply(post.id, commentId, editContent);
        setEditingReply(null);
        setEditContent("");
      },
      {
        successMessage: "Reply updated successfully",
        errorMessage: "Failed to update reply"
      }
    );
  };

  const handleSendReply = async (commentId: string, reply: string) => {
    await executeReplyAction(
      async () => {
        await onSendReply(post.id, commentId, reply);
      },
      {
        successMessage: "Reply sent successfully",
        errorMessage: "Failed to send reply"
      }
    );
  };

  const handleGenerateAIReply = async (commentId: string) => {
    setGeneratingReply(commentId);
    setReplyProgress(0);

    // Simulate AI generation progress
    const progressInterval = setInterval(() => {
      setReplyProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 200);

    try {
      // Simulate AI reply generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      setReplyProgress(100);

      showToast('success', 'AI reply generated', 'Review and send the suggested reply');
    } catch (error) {
      showToast('error', 'Failed to generate AI reply');
    } finally {
      setTimeout(() => {
        setGeneratingReply(null);
        setReplyProgress(0);
        clearInterval(progressInterval);
      }, 500);
    }
  };

  const handleCopyReply = async (reply: string) => {
    try {
      await navigator.clipboard.writeText(reply);
      showToast('success', 'Reply copied to clipboard');
    } catch (error) {
      showToast('error', 'Failed to copy reply');
    }
  };

  const pendingReplies = post.comments.length;
  const avgResponseTime = "2.3 hours"; // This would come from analytics
  const engagementRate = ((post.engagement.likes + post.engagement.comments + post.engagement.shares) / 1000 * 100).toFixed(1);

  return (
    <Card className="shadow-card hover:shadow-md transition-all duration-200 group">
      <CardHeader
        className="cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-3">
            <div className="flex items-center gap-2 flex-wrap">
              <Badge
                variant="outline"
                className={`text-xs ${getPlatformColor(post.platform)}`}
              >
                {post.platform}
              </Badge>
              <span className="text-sm text-muted-foreground">{post.timestamp}</span>
              {pendingReplies > 0 && (
                <Badge variant="secondary" className="bg-primary-light text-primary">
                  {pendingReplies} {pendingReplies === 1 ? 'reply' : 'replies'} pending
                </Badge>
              )}
              <Tooltip>
                <TooltipTrigger>
                  <Badge variant="outline" className="text-xs">
                    <TrendingUp className="w-3 h-3 mr-1" />
                    {engagementRate}%
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Engagement rate</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger>
                  <Badge variant="outline" className="text-xs">
                    <Clock className="w-3 h-3 mr-1" />
                    {avgResponseTime}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Average response time</p>
                </TooltipContent>
              </Tooltip>
            </div>
            
            <div>
              <h3 className="font-semibold text-foreground mb-2">{post.title}</h3>
              <p className="text-muted-foreground text-sm leading-relaxed line-clamp-2">
                {post.content}
              </p>
            </div>

            {/* Engagement Stats */}
            <div className="flex items-center gap-6">
              <Tooltip>
                <TooltipTrigger>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors">
                    <Heart className="w-4 h-4" />
                    <span className="font-medium">{post.engagement.likes}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{post.engagement.likes} likes</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors">
                    <MessageCircle className="w-4 h-4" />
                    <span className="font-medium">{post.engagement.comments}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{post.engagement.comments} comments</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors">
                    <Share className="w-4 h-4" />
                    <span className="font-medium">{post.engagement.shares}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{post.engagement.shares} shares</p>
                </TooltipContent>
              </Tooltip>

              <div className="ml-auto flex items-center gap-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>View original post</p>
                  </TooltipContent>
                </Tooltip>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleCopyReply(post.content)}>
                      <Copy className="w-4 h-4 mr-2" />
                      Copy post content
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <AlertCircle className="w-4 h-4 mr-2" />
                      Mark as priority
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
          
          <Button 
            variant="ghost" 
            size="sm"
            className="ml-4 text-muted-foreground hover:text-foreground"
          >
            {isExpanded ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </Button>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0 border-t">
          <div className="space-y-6 mt-6">
            {post.comments.map((comment) => (
              <div key={comment.id} className="border-l-2 border-muted pl-6 space-y-4">
                {/* Original Comment */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">{comment.author}</span>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${getSentimentColor(comment.sentiment)}`}
                    >
                      {comment.sentiment}
                    </Badge>
                    <span className="text-xs text-muted-foreground">{comment.timestamp}</span>
                  </div>
                  <p className="text-sm text-foreground">{comment.content}</p>
                </div>

                {/* AI Generated Reply */}
                <div className="bg-ai-secondary/50 border border-ai-primary/20 rounded-lg p-4">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-5 h-5 bg-gradient-ai rounded-full flex items-center justify-center">
                            <Brain className="w-3 h-3 text-ai-foreground" />
                          </div>
                          <span className="text-sm font-medium text-ai-primary">
                            AI Suggested Reply
                          </span>
                          <Badge variant="outline" className="text-xs">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            95% confidence
                          </Badge>
                        </div>
                        {generatingReply !== comment.id && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleGenerateAIReply(comment.id)}
                            className="text-xs"
                          >
                            <Brain className="w-3 h-3 mr-1" />
                            Regenerate
                          </Button>
                        )}
                      </div>

                      {generatingReply === comment.id && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Brain className="w-4 h-4 animate-pulse" />
                            <span>Generating AI reply...</span>
                          </div>
                          <ProgressIndicator
                            value={replyProgress}
                            size="sm"
                            variant="default"
                            showValue={false}
                          />
                        </div>
                      )}
                      
                      {editingReply === comment.id ? (
                        <div className="space-y-3">
                          <Textarea
                            value={editContent}
                            onChange={(e) => setEditContent(e.target.value)}
                            className="min-h-[80px] resize-none"
                            placeholder="Edit your reply..."
                          />
                          <div className="flex gap-2">
                            <LoadingButton
                              variant="send"
                              size="sm"
                              onClick={() => handleSaveEdit(comment.id)}
                              loading={false}
                            >
                              <Send className="w-3 h-3 mr-1" />
                              Save & Send
                            </LoadingButton>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEditingReply(null)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <p className="text-sm text-foreground leading-relaxed">
                            {comment.aiReply}
                          </p>
                          <div className="flex gap-2 flex-wrap">
                            <LoadingButton
                              variant="send"
                              size="sm"
                              onClick={() => handleSendReply(comment.id, comment.aiReply)}
                              loading={false}
                            >
                              <Send className="w-3 h-3 mr-1" />
                              Send as is
                            </LoadingButton>
                            <Button
                              variant="edit"
                              size="sm"
                              onClick={() => handleEditReply(comment.id, comment.aiReply)}
                            >
                              <Edit3 className="w-3 h-3 mr-1" />
                              Edit first
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopyReply(comment.aiReply)}
                            >
                              <Copy className="w-3 h-3 mr-1" />
                              Copy
                            </Button>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}
