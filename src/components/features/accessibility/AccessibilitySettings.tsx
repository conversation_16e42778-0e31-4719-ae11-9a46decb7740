import { useState } from "react";
import { Setting<PERSON>, Eye, Type, Keyboard, Volume2, Contrast } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { AccessibleModal } from "@/components/ui/accessible-modal";
import { useAccessibility } from "@/components/providers/AccessibilityProvider";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function AccessibilitySettings() {
  const [isOpen, setIsOpen] = useState(false);
  const { settings, updateSetting } = useAccessibility();

  const settingsConfig = [
    {
      key: 'reducedMotion' as const,
      label: 'Reduced Motion',
      description: 'Minimize animations and transitions',
      icon: <Eye className="w-4 h-4" />,
    },
    {
      key: 'highContrast' as const,
      label: 'High Contrast',
      description: 'Increase contrast for better visibility',
      icon: <Contrast className="w-4 h-4" />,
    },
    {
      key: 'largeText' as const,
      label: 'Large Text',
      description: 'Increase text size throughout the app',
      icon: <Type className="w-4 h-4" />,
    },
    {
      key: 'screenReaderMode' as const,
      label: 'Screen Reader Mode',
      description: 'Optimize for screen reader users',
      icon: <Volume2 className="w-4 h-4" />,
    },
    {
      key: 'keyboardNavigation' as const,
      label: 'Enhanced Keyboard Navigation',
      description: 'Improve keyboard navigation with visible focus indicators',
      icon: <Keyboard className="w-4 h-4" />,
    },
  ];

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(true)}
            aria-label="Open accessibility settings"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Accessibility Settings</p>
        </TooltipContent>
      </Tooltip>

      <AccessibleModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title="Accessibility Settings"
        description="Customize the app to meet your accessibility needs"
        size="md"
      >
        <div className="space-y-6">
          <p className="text-sm text-muted-foreground">
            These settings help make the application more accessible. Changes are saved automatically
            and will persist across sessions.
          </p>

          <div className="space-y-4">
            {settingsConfig.map((setting) => (
              <Card key={setting.key} className="p-4">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex items-start gap-3 flex-1">
                    <div className="mt-1 text-primary">
                      {setting.icon}
                    </div>
                    <div className="flex-1">
                      <Label 
                        htmlFor={setting.key}
                        className="text-sm font-medium cursor-pointer"
                      >
                        {setting.label}
                      </Label>
                      <p className="text-xs text-muted-foreground mt-1">
                        {setting.description}
                      </p>
                    </div>
                  </div>
                  <Switch
                    id={setting.key}
                    checked={settings[setting.key]}
                    onCheckedChange={(checked) => updateSetting(setting.key, checked)}
                    aria-describedby={`${setting.key}-description`}
                  />
                </div>
                <div id={`${setting.key}-description`} className="sr-only">
                  {setting.description}
                </div>
              </Card>
            ))}
          </div>

          <div className="pt-4 border-t">
            <h3 className="text-sm font-medium mb-2">Additional Resources</h3>
            <div className="space-y-2 text-xs text-muted-foreground">
              <p>• Use Tab to navigate between interactive elements</p>
              <p>• Use Enter or Space to activate buttons and links</p>
              <p>• Use Escape to close dialogs and menus</p>
              <p>• Use arrow keys to navigate within menus and lists</p>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => {
                // Reset to defaults
                Object.keys(settings).forEach(key => {
                  updateSetting(key as keyof typeof settings, false);
                });
              }}
            >
              Reset to Defaults
            </Button>
            <Button onClick={() => setIsOpen(false)}>
              Done
            </Button>
          </div>
        </div>
      </AccessibleModal>
    </>
  );
}
