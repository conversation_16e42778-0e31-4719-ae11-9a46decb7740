import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { InteractiveChart } from "@/components/ui/interactive-chart";
import { TrendingUp, TrendingDown, MoreHorizontal, Info, BarChart3 } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { AnalyticsCardProps } from "@/types";
import { cn } from "@/lib/utils";

// Mock chart data - in a real app, this would come from props or API
const generateMockData = (trend: "up" | "down") => {
  const baseValue = 50;
  const data = [];

  for (let i = 0; i < 7; i++) {
    const variation = trend === "up" ? i * 5 + Math.random() * 10 :
                     (7 - i) * 5 + Math.random() * 10;
    data.push({
      name: `Day ${i + 1}`,
      value: Math.round(baseValue + variation),
    });
  }

  return data;
};

export function AnalyticsCard({ title, value, change, trend, icon, description }: AnalyticsCardProps) {
  const [showChart, setShowChart] = useState(false);
  const isPositive = trend === "up";
  const chartData = generateMockData(trend);

  return (
    <Card className="p-6 hover:shadow-card transition-shadow group">
      <div className="flex items-center justify-between mb-4">
        <div className="p-2 bg-primary-light rounded-lg">
          {icon}
        </div>
        <div className="flex items-center gap-2">
          <Badge
            variant={trend === "up" ? "default" : "secondary"}
            className={cn(
              "flex items-center gap-1",
              trend === "up" ? "bg-accent text-accent-foreground" : ""
            )}
          >
            {isPositive ? (
              <TrendingUp className="w-3 h-3" />
            ) : (
              <TrendingDown className="w-3 h-3" />
            )}
            {change}
          </Badge>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48" align="end">
              <div className="space-y-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowChart(!showChart)}
                  className="w-full justify-start"
                >
                  <BarChart3 className="w-4 h-4 mr-2" />
                  {showChart ? 'Hide Chart' : 'Show Chart'}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                >
                  View Details
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                >
                  Export Data
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="flex items-center gap-2 mb-1">
        <h3 className="font-semibold text-lg text-foreground">{value}</h3>
        <Tooltip>
          <TooltipTrigger>
            <Info className="w-4 h-4 text-muted-foreground" />
          </TooltipTrigger>
          <TooltipContent>
            <p className="max-w-xs">{description}</p>
          </TooltipContent>
        </Tooltip>
      </div>

      <p className="text-sm font-medium text-muted-foreground mb-1">{title}</p>
      <p className="text-xs text-muted-foreground">vs last period</p>

      {showChart && (
        <div className="mt-4 pt-4 border-t">
          <InteractiveChart
            data={chartData}
            type="area"
            height={120}
            color={isPositive ? 'hsl(var(--accent))' : 'hsl(var(--destructive))'}
            gradient={true}
            showGrid={false}
            animate={true}
          />
        </div>
      )}
    </Card>
  );
}
