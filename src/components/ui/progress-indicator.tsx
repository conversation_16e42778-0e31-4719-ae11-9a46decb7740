import * as React from "react"
import { cn } from "@/lib/utils"

export interface ProgressIndicatorProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number
  max?: number
  size?: "sm" | "md" | "lg"
  variant?: "default" | "success" | "warning" | "error"
  showValue?: boolean
}

const ProgressIndicator = React.forwardRef<HTMLDivElement, ProgressIndicatorProps>(
  ({ className, value = 0, max = 100, size = "md", variant = "default", showValue = false, ...props }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
    
    const sizeClasses = {
      sm: "h-1",
      md: "h-2",
      lg: "h-3"
    }
    
    const variantClasses = {
      default: "bg-primary",
      success: "bg-accent",
      warning: "bg-warning",
      error: "bg-destructive"
    }

    return (
      <div className={cn("w-full", className)} ref={ref} {...props}>
        {showValue && (
          <div className="flex justify-between text-sm text-muted-foreground mb-1">
            <span>Progress</span>
            <span>{Math.round(percentage)}%</span>
          </div>
        )}
        <div className={cn("w-full bg-muted rounded-full overflow-hidden", sizeClasses[size])}>
          <div
            className={cn("h-full transition-all duration-300 ease-out", variantClasses[variant])}
            style={{ width: `${percentage}%` }}
          />
        </div>
      </div>
    )
  }
)
ProgressIndicator.displayName = "ProgressIndicator"

export { ProgressIndicator }
