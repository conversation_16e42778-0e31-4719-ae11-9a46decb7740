import React from 'react';
import { RefreshCw } from 'lucide-react';
import { usePullToRefresh } from '@/hooks/use-pull-to-refresh';
import { cn } from '@/lib/utils';

export interface PullToRefreshProps {
  onRefresh: () => Promise<void> | void;
  children: React.ReactNode;
  disabled?: boolean;
  threshold?: number;
  className?: string;
}

export function PullToRefresh({
  onRefresh,
  children,
  disabled = false,
  threshold = 80,
  className,
}: PullToRefreshProps) {
  const { elementRef, isPulling, isRefreshing, pullDistance } = usePullToRefresh({
    onRefresh,
    disabled,
    threshold,
  });

  const progress = Math.min(pullDistance / threshold, 1);
  const rotation = progress * 180;

  return (
    <div
      ref={elementRef}
      className={cn('relative overflow-auto', className)}
      style={{
        transform: isRefreshing ? `translateY(${threshold}px)` : `translateY(${Math.min(pullDistance, threshold)}px)`,
        transition: isRefreshing || pullDistance === 0 ? 'transform 0.3s ease-out' : 'none',
      }}
    >
      {/* Pull to refresh indicator */}
      <div
        className={cn(
          'absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full',
          'flex items-center justify-center',
          'w-12 h-12 bg-background border rounded-full shadow-lg',
          'transition-all duration-200 ease-out',
          'z-50'
        )}
        style={{
          transform: `translateX(-50%) translateY(${Math.min(pullDistance - threshold, 0)}px)`,
          opacity: pullDistance > 20 ? 1 : 0,
        }}
      >
        <RefreshCw
          className={cn(
            'w-5 h-5 text-primary transition-transform duration-200',
            isRefreshing && 'animate-spin',
            isPulling && !isRefreshing && 'text-accent'
          )}
          style={{
            transform: !isRefreshing ? `rotate(${rotation}deg)` : undefined,
          }}
        />
      </div>

      {/* Content */}
      {children}
    </div>
  );
}
