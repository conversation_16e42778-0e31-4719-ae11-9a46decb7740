import React, { createContext, useContext, useState, useEffect } from 'react';
import { useScreenReader } from '@/hooks/use-screen-reader';

export interface AccessibilitySettings {
  reducedMotion: boolean;
  highContrast: boolean;
  largeText: boolean;
  screenReaderMode: boolean;
  keyboardNavigation: boolean;
}

export interface AccessibilityContextType {
  settings: AccessibilitySettings;
  updateSetting: (key: keyof AccessibilitySettings, value: boolean) => void;
  announceToScreenReader: (message: string, priority?: 'polite' | 'assertive') => void;
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
}

export interface AccessibilityProviderProps {
  children: React.ReactNode;
}

export function AccessibilityProvider({ children }: AccessibilityProviderProps) {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    reducedMotion: false,
    highContrast: false,
    largeText: false,
    screenReaderMode: false,
    keyboardNavigation: true,
  });

  const { announce } = useScreenReader();

  // Detect user preferences
  useEffect(() => {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    // Check for high contrast preference
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
    
    // Load saved preferences
    const savedSettings = localStorage.getItem('accessibility-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Failed to parse accessibility settings:', error);
      }
    }

    // Apply system preferences if no saved settings
    setSettings(prev => ({
      ...prev,
      reducedMotion: prefersReducedMotion,
      highContrast: prefersHighContrast,
    }));
  }, []);

  // Apply settings to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Reduced motion
    if (settings.reducedMotion) {
      root.style.setProperty('--animation-duration', '0s');
      root.style.setProperty('--transition-duration', '0s');
    } else {
      root.style.removeProperty('--animation-duration');
      root.style.removeProperty('--transition-duration');
    }

    // High contrast
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Large text
    if (settings.largeText) {
      root.classList.add('large-text');
    } else {
      root.classList.remove('large-text');
    }

    // Screen reader mode
    if (settings.screenReaderMode) {
      root.classList.add('screen-reader-mode');
    } else {
      root.classList.remove('screen-reader-mode');
    }

    // Keyboard navigation
    if (settings.keyboardNavigation) {
      root.classList.add('keyboard-navigation');
    } else {
      root.classList.remove('keyboard-navigation');
    }

    // Save settings
    localStorage.setItem('accessibility-settings', JSON.stringify(settings));
  }, [settings]);

  const updateSetting = (key: keyof AccessibilitySettings, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    
    // Announce the change
    const settingNames = {
      reducedMotion: 'Reduced motion',
      highContrast: 'High contrast',
      largeText: 'Large text',
      screenReaderMode: 'Screen reader mode',
      keyboardNavigation: 'Keyboard navigation',
    };
    
    announce({
      message: `${settingNames[key]} ${value ? 'enabled' : 'disabled'}`,
      priority: 'polite',
    });
  };

  const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    announce({ message, priority });
  };

  const value: AccessibilityContextType = {
    settings,
    updateSetting,
    announceToScreenReader,
  };

  return (
    <AccessibilityContext.Provider value={value}>
      {children}
    </AccessibilityContext.Provider>
  );
}

// CSS for accessibility features
export const accessibilityStyles = `
  /* High contrast mode */
  .high-contrast {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 10%;
    --card-foreground: 0 0% 100%;
    --primary: 210 100% 70%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 85%;
    --accent: 120 100% 70%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 100% 70%;
    --destructive-foreground: 0 0% 0%;
    --border: 0 0% 30%;
    --input: 0 0% 20%;
    --ring: 210 100% 70%;
  }

  /* Large text mode */
  .large-text {
    font-size: 1.125rem;
  }

  .large-text .text-sm {
    font-size: 1rem;
  }

  .large-text .text-xs {
    font-size: 0.875rem;
  }

  /* Screen reader mode */
  .screen-reader-mode .sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: 0.25rem !important;
    margin: 0.25rem !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
    background: var(--muted);
    border: 1px solid var(--border);
    border-radius: 0.25rem;
  }

  /* Keyboard navigation */
  .keyboard-navigation *:focus {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
  }

  .keyboard-navigation button:focus,
  .keyboard-navigation input:focus,
  .keyboard-navigation select:focus,
  .keyboard-navigation textarea:focus,
  .keyboard-navigation a:focus {
    box-shadow: 0 0 0 2px var(--ring);
  }

  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
`;
