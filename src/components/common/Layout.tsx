import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/features/navigation";
import { AccessibilitySettings } from "@/components/features/accessibility";
import { LayoutProps } from "@/types";
import { useIsMobile } from "@/hooks/use-mobile";
import { useLocation } from "react-router-dom";

export function Layout({ children }: LayoutProps) {
  const isMobile = useIsMobile();
  const location = useLocation();
  const isAnalyticsPage = location.pathname.includes('/analytics');

  return (
    <SidebarProvider defaultOpen={!isMobile}>
      {/* Skip to main content link for keyboard users */}
      <a
        href="#main-content"
        className="skip-link"
        onFocus={(e) => e.target.scrollIntoView()}
      >
        Skip to main content
      </a>

      <div className="min-h-screen flex w-full">
        <AppSidebar />

        <div className="flex-1 flex flex-col">
          <main id="main-content" className="flex-1" tabIndex={-1}>
            {children}
          </main>
        </div>



        {/* Accessibility Settings - positioned for easy access */}
        {!isAnalyticsPage && (
          <div className={`fixed ${isMobile ? 'bottom-4 right-4' : 'top-4 right-4'} z-50`}>
            <div className="bg-background/80 backdrop-blur border shadow-sm rounded-md">
              <AccessibilitySettings />
            </div>
          </div>
        )}
      </div>
    </SidebarProvider>
  );
}
