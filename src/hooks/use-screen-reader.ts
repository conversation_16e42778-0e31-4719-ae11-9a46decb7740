import { useEffect, useCallback } from 'react';

export interface ScreenReaderAnnouncement {
  message: string;
  priority?: 'polite' | 'assertive';
  delay?: number;
}

export function useScreenReader() {
  const announce = useCallback((announcement: ScreenReaderAnnouncement) => {
    const { message, priority = 'polite', delay = 0 } = announcement;

    const announceMessage = () => {
      // Create a live region element
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', priority);
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.setAttribute('class', 'sr-only');
      liveRegion.style.position = 'absolute';
      liveRegion.style.left = '-10000px';
      liveRegion.style.width = '1px';
      liveRegion.style.height = '1px';
      liveRegion.style.overflow = 'hidden';

      document.body.appendChild(liveRegion);

      // Add the message
      liveRegion.textContent = message;

      // Remove the element after announcement
      setTimeout(() => {
        if (document.body.contains(liveRegion)) {
          document.body.removeChild(liveRegion);
        }
      }, 1000);
    };

    if (delay > 0) {
      setTimeout(announceMessage, delay);
    } else {
      announceMessage();
    }
  }, []);

  const announceNavigation = useCallback((destination: string) => {
    announce({
      message: `Navigated to ${destination}`,
      priority: 'polite',
    });
  }, [announce]);

  const announceAction = useCallback((action: string, result?: string) => {
    const message = result ? `${action}. ${result}` : action;
    announce({
      message,
      priority: 'assertive',
    });
  }, [announce]);

  const announceError = useCallback((error: string) => {
    announce({
      message: `Error: ${error}`,
      priority: 'assertive',
    });
  }, [announce]);

  const announceSuccess = useCallback((success: string) => {
    announce({
      message: `Success: ${success}`,
      priority: 'polite',
    });
  }, [announce]);

  const announceLoading = useCallback((isLoading: boolean, context?: string) => {
    const message = isLoading 
      ? `Loading${context ? ` ${context}` : ''}...`
      : `Finished loading${context ? ` ${context}` : ''}`;
    
    announce({
      message,
      priority: 'polite',
    });
  }, [announce]);

  return {
    announce,
    announceNavigation,
    announceAction,
    announceError,
    announceSuccess,
    announceLoading,
  };
}

// Hook for managing ARIA attributes
export function useAriaAttributes() {
  const setAriaLabel = useCallback((element: HTMLElement | null, label: string) => {
    if (element) {
      element.setAttribute('aria-label', label);
    }
  }, []);

  const setAriaDescribedBy = useCallback((element: HTMLElement | null, id: string) => {
    if (element) {
      element.setAttribute('aria-describedby', id);
    }
  }, []);

  const setAriaExpanded = useCallback((element: HTMLElement | null, expanded: boolean) => {
    if (element) {
      element.setAttribute('aria-expanded', expanded.toString());
    }
  }, []);

  const setAriaSelected = useCallback((element: HTMLElement | null, selected: boolean) => {
    if (element) {
      element.setAttribute('aria-selected', selected.toString());
    }
  }, []);

  const setAriaPressed = useCallback((element: HTMLElement | null, pressed: boolean) => {
    if (element) {
      element.setAttribute('aria-pressed', pressed.toString());
    }
  }, []);

  const setAriaHidden = useCallback((element: HTMLElement | null, hidden: boolean) => {
    if (element) {
      element.setAttribute('aria-hidden', hidden.toString());
    }
  }, []);

  const setRole = useCallback((element: HTMLElement | null, role: string) => {
    if (element) {
      element.setAttribute('role', role);
    }
  }, []);

  return {
    setAriaLabel,
    setAriaDescribedBy,
    setAriaExpanded,
    setAriaSelected,
    setAriaPressed,
    setAriaHidden,
    setRole,
  };
}
