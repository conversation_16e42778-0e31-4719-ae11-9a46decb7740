import { useState, useEffect, useCallback } from 'react';
import { SearchSuggestion } from '@/components/features/search/SearchWithSuggestions';

export interface UseSearchOptions {
  debounceMs?: number;
  maxRecentSearches?: number;
  storageKey?: string;
}

export function useSearch(options: UseSearchOptions = {}) {
  const {
    debounceMs = 300,
    maxRecentSearches = 10,
    storageKey = 'brand-management-recent-searches',
  } = options;

  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);

  // Load recent searches from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        setRecentSearches(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Failed to load recent searches:', error);
    }
  }, [storageKey]);

  // Debounce query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [query, debounceMs]);

  // Generate suggestions based on query
  useEffect(() => {
    if (!debouncedQuery.trim()) {
      setSuggestions([]);
      return;
    }

    // Mock suggestions - in a real app, this would come from an API
    const mockSuggestions: SearchSuggestion[] = [
      { id: '1', text: 'customer service', type: 'trending', count: 45 },
      { id: '2', text: 'product feedback', type: 'trending', count: 32 },
      { id: '3', text: 'bug report', type: 'suggestion', count: 18 },
      { id: '4', text: 'feature request', type: 'suggestion', count: 12 },
      { id: '5', text: 'pricing question', type: 'suggestion', count: 8 },
      { id: '6', text: 'technical support', type: 'trending', count: 67 },
      { id: '7', text: 'account issue', type: 'suggestion', count: 23 },
      { id: '8', text: 'billing inquiry', type: 'suggestion', count: 15 },
    ];

    const filtered = mockSuggestions.filter(suggestion =>
      suggestion.text.toLowerCase().includes(debouncedQuery.toLowerCase())
    );

    setSuggestions(filtered);
  }, [debouncedQuery]);

  const addToRecentSearches = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) return;

    setRecentSearches(prev => {
      const filtered = prev.filter(item => item !== searchQuery);
      const updated = [searchQuery, ...filtered].slice(0, maxRecentSearches);
      
      try {
        localStorage.setItem(storageKey, JSON.stringify(updated));
      } catch (error) {
        console.error('Failed to save recent searches:', error);
      }
      
      return updated;
    });
  }, [maxRecentSearches, storageKey]);

  const clearRecentSearches = useCallback(() => {
    setRecentSearches([]);
    try {
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.error('Failed to clear recent searches:', error);
    }
  }, [storageKey]);

  const handleSearch = useCallback((searchQuery: string) => {
    addToRecentSearches(searchQuery);
  }, [addToRecentSearches]);

  return {
    query,
    setQuery,
    debouncedQuery,
    recentSearches,
    suggestions,
    handleSearch,
    clearRecentSearches,
  };
}
