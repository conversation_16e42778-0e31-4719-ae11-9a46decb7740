import { useState, useCallback } from 'react';

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  success: string | null;
}

export interface UseLoadingReturn extends LoadingState {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSuccess: (success: string | null) => void;
  clearMessages: () => void;
  executeAsync: <T>(
    asyncFn: () => Promise<T>,
    options?: {
      successMessage?: string;
      errorMessage?: string;
    }
  ) => Promise<T | null>;
}

export function useLoading(initialState: Partial<LoadingState> = {}): UseLoadingReturn {
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    error: null,
    success: null,
    ...initialState,
  });

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error, success: null }));
  }, []);

  const setSuccess = useCallback((success: string | null) => {
    setState(prev => ({ ...prev, success, error: null }));
  }, []);

  const clearMessages = useCallback(() => {
    setState(prev => ({ ...prev, error: null, success: null }));
  }, []);

  const executeAsync = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    options: {
      successMessage?: string;
      errorMessage?: string;
    } = {}
  ): Promise<T | null> => {
    try {
      setLoading(true);
      clearMessages();
      
      const result = await asyncFn();
      
      if (options.successMessage) {
        setSuccess(options.successMessage);
      }
      
      return result;
    } catch (error) {
      const errorMessage = options.errorMessage || 
        (error instanceof Error ? error.message : 'An unexpected error occurred');
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [setLoading, clearMessages, setSuccess, setError]);

  return {
    ...state,
    setLoading,
    setError,
    setSuccess,
    clearMessages,
    executeAsync,
  };
}

// Specialized hooks for common use cases
export function usePostLoading() {
  return useLoading();
}

export function useReplyLoading() {
  return useLoading();
}

export function useAnalyticsLoading() {
  return useLoading();
}
