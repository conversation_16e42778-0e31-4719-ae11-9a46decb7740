import { T<PERSON>dingUp, MessageSquare, Building2, Users, BarChart3 } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

const Index = () => {
  return (
    <div className="flex-1 flex items-center justify-center p-6 bg-gradient-subtle min-h-screen">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-foreground">Brand Response AI</h1>
        <p className="text-xl text-muted-foreground">
          Select a brand from the sidebar to manage responses
        </p>
        <p className="text-muted-foreground">
          Use the menu button at the bottom left to open the sidebar
        </p>
      </div>
    </div>
  );
};

export default Index;